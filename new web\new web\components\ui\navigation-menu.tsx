// components/animated-nav-framer.tsx
"use client";

import * as React from "react";
import { motion, useScroll, useMotionValueEvent, AnimatePresence } from "framer-motion";
import { Navigation, Menu, ChevronDown, X } from "lucide-react";
import { cn } from "@/lib/utils";
import Image from "next/image";

const navItems = [
  { name: "Home", href: "/" },
  { name: "About Us", href: "/about" },
  {
    name: "Services",
    href: "/services",
    dropdown: [
      { name: "Buy Property", href: "/services/buy" },
      { name: "Lease Property", href: "/services/lease" },
      { name: "Pre-Lease", href: "/services/pre-lease" },
      { name: "Sell Property", href: "/services/sell" },
      { name: "Investment", href: "/services/investment" },
    ]
  },
  { 
    name: "Projects", 
    href: "/projects",
    dropdown: [
      { name: "New", href: "/projects/new" },
      { name: "Resale", href: "/projects/resale" },
    ]
  },
  { name: "Blog", href: "/blog" },
  { name: "Calculator", href: "/calculator" },
  { name: "Career", href: "/career" },
  { name: "Contact Us", href: "/contact" },
];

const EXPAND_SCROLL_THRESHOLD = 100;

const containerVariants = {
  expanded: {
    y: 0,
    opacity: 1,
    width: "auto",
    transition: {
      y: { type: "spring", damping: 18, stiffness: 250 },
      opacity: { duration: 0.3 },
      type: "spring",
      damping: 20,
      stiffness: 300,
      staggerChildren: 0.07,
      delayChildren: 0.2,
    },
  },
  collapsed: {
    y: 0,
    opacity: 1,
    width: "3.5rem",
    height: "3.5rem",
    transition: {
      type: "spring",
      damping: 20,
      stiffness: 300,
      when: "afterChildren",
      staggerChildren: 0.05,
      staggerDirection: -1,
    },
  },
};

const logoVariants = {
  expanded: { opacity: 1, x: 0, rotate: 0, transition: { type: "spring", damping: 15 } },
  collapsed: { opacity: 0, x: -25, rotate: -180, transition: { duration: 0.3 } },
};

const itemVariants = {
  expanded: { opacity: 1, x: 0, scale: 1, transition: { type: "spring", damping: 15 } },
  collapsed: { opacity: 0, x: -20, scale: 0.95, transition: { duration: 0.2 } },
};

const collapsedIconVariants = {
    expanded: { opacity: 0, scale: 0.8, transition: { duration: 0.2 } },
    collapsed: { 
      opacity: 1, 
      scale: 1,
      transition: {
        type: "spring",
        damping: 15,
        stiffness: 300,
        delay: 0.15,
      }
    },
}

const mobileMenuVariants = {
  hidden: {
    opacity: 0,
    y: -20,
    scale: 0.95,
    transition: {
      duration: 0.3,
      ease: "easeInOut"
    }
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.4,
      ease: "easeOut",
      staggerChildren: 0.08,
      delayChildren: 0.1
    }
  }
};

const mobileItemVariants = {
  hidden: { opacity: 0, x: -20, y: 10 },
  visible: { 
    opacity: 1, 
    x: 0, 
    y: 0,
    transition: {
      type: "spring",
      damping: 20,
      stiffness: 300
    }
  }
};

export function AnimatedNavFramer() {
  const [isExpanded, setExpanded] = React.useState(true);
  const [activeDropdown, setActiveDropdown] = React.useState<string | null>(null);
  const [showCollapsedMenu, setShowCollapsedMenu] = React.useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);
  const [mobileActiveDropdown, setMobileActiveDropdown] = React.useState<string | null>(null);
  const [mounted, setMounted] = React.useState(false);
  
  const { scrollY } = useScroll();
  const lastScrollY = React.useRef(0);
  const scrollPositionOnCollapse = React.useRef(0);

  // Mount component
  React.useEffect(() => {
    setMounted(true);
  }, []);

  useMotionValueEvent(scrollY, "change", (latest) => {
    if (!mounted) return;
    
    const previous = lastScrollY.current;
    
    // Only apply scroll behavior on desktop
    if (typeof window !== 'undefined' && window.innerWidth >= 768) {
      if (isExpanded && latest > previous && latest > 150) {
        setExpanded(false);
        scrollPositionOnCollapse.current = latest; 
      } 
      else if (!isExpanded && latest < previous && (scrollPositionOnCollapse.current - latest > EXPAND_SCROLL_THRESHOLD)) {
        setExpanded(true);
      }
    }
    
    lastScrollY.current = latest;
  });

  const handleNavClick = (e: React.MouseEvent) => {
    if (!isExpanded && typeof window !== 'undefined' && window.innerWidth >= 768) {
      e.preventDefault();
      e.stopPropagation();
      
      // Single click - expand navbar directly
      setExpanded(true);
      setShowCollapsedMenu(false);
    }
  };

  const handleMobileMenuToggle = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // Close collapsed menu when expanding
  React.useEffect(() => {
    if (isExpanded) {
      setShowCollapsedMenu(false);
    }
  }, [isExpanded]);

  // Close menus when clicking outside
  React.useEffect(() => {
    if (!mounted) return;
    
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      
      // Don't close if clicking inside mobile menu
      if (isMobileMenuOpen && target.closest('[data-mobile-menu]')) {
        return;
      }
      
      setShowCollapsedMenu(false);
      setActiveDropdown(null);
      setIsMobileMenuOpen(false);
      setMobileActiveDropdown(null);
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [mounted, isMobileMenuOpen]);

  // Prevent body scroll when mobile menu is open
  React.useEffect(() => {
    if (!mounted) return;
    
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen, mounted]);

  // Don't render until mounted to prevent hydration mismatch
  if (!mounted) {
    return (
      <div className="relative">
        <div className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 hidden md:block">
          <div className="flex items-center rounded-full border bg-background/80 shadow-lg backdrop-blur-sm h-12 px-4">
            <Image 
              src="/logo/logo.png" 
              alt="Dwelling Desire" 
              width={40} 
              height={40} 
              className="h-10 w-auto" 
              priority
            />
            <div className="flex items-center gap-4 pr-12 whitespace-nowrap ml-4">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="text-sm font-medium text-[#808185] hover:text-[#B9224B] transition-colors px-2 py-1 flex items-center gap-1"
                >
                  {item.name}
                  {item.dropdown && (
                    <ChevronDown className="h-3 w-3" />
                  )}
                </a>
              ))}
            </div>
          </div>
        </div>
        
        <div className="fixed top-4 left-4 right-4 z-50 md:hidden flex justify-between items-center">
          {/* Logo */}
          <div className="flex items-center">
            <Image 
              src="/logo/logo.png" 
              alt="Dwelling Desire" 
              width={48} 
              height={48} 
              className="h-12 w-auto" 
              priority
            />
          </div>
          
          {/* Menu Button */}
          <div className="flex items-center justify-center w-14 h-14 rounded-full border bg-background/80 shadow-lg backdrop-blur-sm">
            <Menu className="h-6 w-6" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <React.Fragment>
      {/* Desktop Navigation */}
      <div className="fixed top-6 left-1/2 transform -translate-x-1/2 z-[100] hidden md:block">
        <motion.nav
          initial={{ y: -80, opacity: 0 }}
          animate={isExpanded ? "expanded" : "collapsed"}
          variants={containerVariants}
          whileHover={!isExpanded ? { scale: 1.1 } : {}}
          whileTap={!isExpanded ? { scale: 0.95 } : {}}
          onClick={handleNavClick}
          className={cn(
            "flex items-center rounded-full border bg-background/80 shadow-lg backdrop-blur-sm h-12",
            !isExpanded && "cursor-pointer justify-center w-12 aspect-square"
          )}
        >
          <motion.div
            variants={logoVariants}
            className="flex-shrink-0 flex items-center pl-4 pr-2"
          >
            <Image 
              src="/logo/logo.png" 
              alt="Dwelling Desire" 
              width={40} 
              height={40} 
              className="h-10 w-auto" 
              priority
            />
          </motion.div>
          
          <motion.div
            className={cn(
              "flex items-center gap-4 pr-12 whitespace-nowrap",
              !isExpanded && "pointer-events-none"
            )}
          >
            {navItems.map((item) => (
              <motion.div
                key={item.name}
                variants={itemVariants}
                className="relative"
                onMouseEnter={() => {
                  if (isExpanded && item.dropdown) {
                    setActiveDropdown(item.name);
                  }
                }}
                onMouseLeave={() => {
                  if (isExpanded && item.dropdown) {
                    setActiveDropdown(null);
                  }
                }}
              >
                <motion.a
                  href={item.href}
                  onClick={(e) => e.stopPropagation()}
                  className={cn(
                    "text-sm font-medium text-[#808185] hover:text-[#B9224B] transition-colors px-2 py-1 flex items-center gap-1",
                    item.dropdown && "cursor-pointer"
                  )}
                >
                  {item.name}
                  {item.dropdown && (
                    <ChevronDown 
                      className={cn(
                        "h-3 w-3 transition-transform duration-200",
                        activeDropdown === item.name && "rotate-180"
                      )} 
                    />
                  )}
                </motion.a>
                
                {/* Dropdown Menu */}
                {item.dropdown && (
                  <AnimatePresence>
                    {activeDropdown === item.name && isExpanded && (
                      <motion.div
                        initial={{ opacity: 0, y: 10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: 10, scale: 0.95 }}
                        transition={{ duration: 0.2 }}
                        className="absolute top-full left-0 mt-2 min-w-[200px] bg-background/95 backdrop-blur-xl border border-border rounded-xl shadow-lg overflow-hidden z-[110]"
                      >
                        {item.dropdown.map((dropdownItem, index) => (
                          <motion.a
                            key={dropdownItem.name}
                            href={dropdownItem.href}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.05 }}
                            className="block px-4 py-3 text-sm text-muted-foreground hover:text-foreground transition-colors"
                            onClick={(e) => {
                              e.stopPropagation();
                              setActiveDropdown(null);
                            }}
                          >
                            {dropdownItem.name}
                          </motion.a>
                        ))}
                      </motion.div>
                    )}
                  </AnimatePresence>
                )}
              </motion.div>
            ))}
          </motion.div>
          
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <motion.div
              variants={collapsedIconVariants}
              animate={isExpanded ? "expanded" : "collapsed"}
            >
              <Menu className="h-6 w-6" />
            </motion.div>
          </div>
          
          {/* Collapsed State Dropdown */}
          <AnimatePresence>
            {!isExpanded && showCollapsedMenu && (
              <motion.div
                initial={{ opacity: 0, y: 10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: 10, scale: 0.95 }}
                transition={{ duration: 0.3 }}
                className="absolute top-full left-1/2 -translate-x-1/2 mt-4 min-w-[200px] bg-background/95 backdrop-blur-xl border border-border rounded-xl shadow-lg overflow-hidden z-[110]"
              >
                {navItems.map((item, index) => (
                  <div key={item.name}>
                    <motion.a
                      href={item.href}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className="block px-4 py-3 text-sm text-muted-foreground hover:text-foreground transition-colors font-medium"
                      onClick={(e) => e.stopPropagation()}
                    >
                      {item.name}
                    </motion.a>
                    {item.dropdown && (
                      <div className="pl-4 border-l border-border/50 ml-4">
                        {item.dropdown.map((dropdownItem, subIndex) => (
                          <motion.a
                            key={dropdownItem.name}
                            href={dropdownItem.href}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: (index * 0.05) + (subIndex * 0.03) }}
                            className="block px-3 py-2 text-xs text-muted-foreground hover:text-foreground transition-colors"
                            onClick={(e) => e.stopPropagation()}
                          >
                            {dropdownItem.name}
                          </motion.a>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.nav>
      </div>

      {/* Desktop Dropdown Backdrop */}
      <AnimatePresence>
        {(activeDropdown || showCollapsedMenu) && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-[90] hidden md:block"
            onClick={() => {
              setActiveDropdown(null);
              setShowCollapsedMenu(false);
            }}
          />
        )}
      </AnimatePresence>

      {/* Mobile Navigation */}
      <div className="fixed top-4 left-4 right-4 z-50 md:hidden flex justify-between items-center">
        {/* Logo */}
        <div className="flex items-center">
          <Image 
            src="/logo/logo.png" 
            alt="Dwelling Desire" 
            width={40} 
            height={40} 
            className="h-10 w-auto" 
            priority
          />
        </div>
        
        {/* Hamburger Menu */}
        <motion.button
          initial={{ y: -80, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ 
            type: "spring", 
            damping: 18, 
            stiffness: 250,
            delay: 0.1
          }}
          onClick={handleMobileMenuToggle}
          className="flex items-center justify-center w-14 h-14 rounded-full border bg-background/80 shadow-lg backdrop-blur-sm hover:scale-105 transition-transform"
        >
          <motion.div
            animate={{ 
              rotate: isMobileMenuOpen ? 90 : 0,
              scale: isMobileMenuOpen ? 0.9 : 1
            }}
            transition={{ duration: 0.3 }}
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </motion.div>
        </motion.button>
      </div>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <React.Fragment>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="fixed inset-0 bg-background/80 backdrop-blur-md z-40 md:hidden"
              onClick={() => setIsMobileMenuOpen(false)}
            />
            
            <motion.div
              data-mobile-menu
              initial="hidden"
              animate="visible"
              exit="hidden"
              variants={mobileMenuVariants}
              className="fixed top-24 left-4 right-4 bg-background/95 backdrop-blur-xl border border-border rounded-2xl shadow-2xl overflow-hidden z-50 md:hidden"
            >
              <div className="p-4 space-y-2">
                {navItems.map((item, index) => (
                  <motion.div
                    key={item.name}
                    variants={mobileItemVariants}
                    className="space-y-1"
                  >
                    <div className="flex items-center justify-between">
                      <motion.a
                        href={item.href}
                        className="text-sm font-medium text-foreground hover:text-primary transition-colors py-2 flex-1"
                        onClick={(e) => {
                          if (item.dropdown) {
                            e.preventDefault();
                            setMobileActiveDropdown(
                              mobileActiveDropdown === item.name ? null : item.name
                            );
                          } else {
                            setIsMobileMenuOpen(false);
                          }
                        }}
                      >
                        {item.name}
                      </motion.a>
                      {item.dropdown && (
                        <motion.button
                          onClick={() => {
                            setMobileActiveDropdown(
                              mobileActiveDropdown === item.name ? null : item.name
                            );
                          }}
                          className="p-1 hover:bg-accent rounded-md transition-colors"
                          whileTap={{ scale: 0.95 }}
                        >
                          <motion.div
                            animate={{ 
                              rotate: mobileActiveDropdown === item.name ? 180 : 0 
                            }}
                            transition={{ duration: 0.2 }}
                          >
                            <ChevronDown className="h-4 w-4" />
                          </motion.div>
                        </motion.button>
                      )}
                    </div>
                    
                    {/* Mobile Dropdown */}
                    <AnimatePresence>
                      {item.dropdown && mobileActiveDropdown === item.name && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="pl-4 border-l-2 border-border/30 space-y-1"
                        >
                          {item.dropdown.map((dropdownItem, subIndex) => (
                            <motion.a
                              key={dropdownItem.name}
                              href={dropdownItem.href}
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: subIndex * 0.05 }}
                              className="block text-xs text-muted-foreground hover:text-foreground transition-colors py-2"
                              onClick={() => setIsMobileMenuOpen(false)}
                            >
                              {dropdownItem.name}
                            </motion.a>
                          ))}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </React.Fragment>
        )}
      </AnimatePresence>
    </React.Fragment>
  );
}