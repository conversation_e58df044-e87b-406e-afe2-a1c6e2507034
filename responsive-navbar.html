<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dwelling Navbar</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <nav>
            <div class="logo">
                <img src="/assets/logo.png" alt="Logo">
            </div>
            <ul class="nav-links">
                <li><a href="#">Home</a></li>
                <li><a href="#">About Us</a></li>
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">Services</a>
                    <ul class="dropdown-menu">
                        <li><a href="#">Buy Property</a></li>
                        <li><a href="#">Lease Property</a></li>
                        <li><a href="#">Pre-Lease</a></li>
                        <li><a href="#">Sell Property</a></li>
                        <li><a href="#">Investment</a></li>
                    </ul>
                </li>
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">Projects</a>
                    <ul class="dropdown-menu">
                        <li><a href="#">New Launch</a></li>
                        <li><a href="#">Resale</a></li>
                    </ul>
                </li>
                <li><a href="#">Insight</a></li>
                <li><a href="#">Calculator</a></li>
                <li><a href="#">Career</a></li>
            </ul>

            <!-- Mobile Menu Button -->
            <div class="mobile-menu-btn" id="mobileMenuBtn">
                <span></span>
                <span></span>
                <span></span>
            </div>

            <!-- Mobile Menu Overlay -->
            <div class="mobile-menu-overlay" id="mobileMenuOverlay">
                <div class="mobile-menu">
                    <div class="mobile-menu-header">
                        <div class="mobile-logo">
                            <img src="/assets/logo.png" alt="Logo">
                        </div>
                        <button class="mobile-close-btn" id="mobileCloseBtn">&times;</button>
                    </div>
                    <ul class="mobile-nav-links">
                        <li><a href="#">Home</a></li>
                        <li><a href="#">About Us</a></li>
                        <li class="mobile-dropdown">
                            <a href="#" class="mobile-dropdown-toggle">Services</a>
                            <ul class="mobile-dropdown-menu">
                                <li><a href="#">Buy Property</a></li>
                                <li><a href="#">Lease Property</a></li>
                                <li><a href="#">Pre-Lease</a></li>
                                <li><a href="#">Sell Property</a></li>
                                <li><a href="#">Investment</a></li>
                            </ul>
                        </li>
                        <li class="mobile-dropdown">
                            <a href="#" class="mobile-dropdown-toggle">Projects</a>
                            <ul class="mobile-dropdown-menu">
                                <li><a href="#">New Launch</a></li>
                                <li><a href="#">Resale</a></li>
                            </ul>
                        </li>
                        <li><a href="#">Insight</a></li>
                        <li><a href="#">Calculator</a></li>
                        <li><a href="#">Career</a></li>
                    </ul>
                </div>
            </div>
</nav>
    </div>

    <script>
        // Mobile Menu Functionality
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
        const mobileCloseBtn = document.getElementById('mobileCloseBtn');
        const mobileDropdownToggles = document.querySelectorAll('.mobile-dropdown-toggle');

        // Open mobile menu
        mobileMenuBtn.addEventListener('click', () => {
            mobileMenuOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        });

        // Close mobile menu
        mobileCloseBtn.addEventListener('click', () => {
            mobileMenuOverlay.classList.remove('active');
            document.body.style.overflow = 'auto';
        });

        // Close menu when clicking overlay
        mobileMenuOverlay.addEventListener('click', (e) => {
            if (e.target === mobileMenuOverlay) {
                mobileMenuOverlay.classList.remove('active');
                document.body.style.overflow = 'auto';
            }
        });

        // Mobile dropdown functionality
        mobileDropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                e.preventDefault();
                const parent = toggle.parentElement;
                const dropdown = parent.querySelector('.mobile-dropdown-menu');

                // Close other dropdowns
                mobileDropdownToggles.forEach(otherToggle => {
                    if (otherToggle !== toggle) {
                        otherToggle.parentElement.classList.remove('active');
                    }
                });

                // Toggle current dropdown
                parent.classList.toggle('active');
            });
        });

        // Close mobile menu on window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                mobileMenuOverlay.classList.remove('active');
                document.body.style.overflow = 'auto';
            }
        });
    </script>
</body>
</html>