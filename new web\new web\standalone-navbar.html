<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dwelling Desire - Premium Real Estate</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .navbar {
            position: fixed;
            top: 24px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .navbar.expanded {
            padding: 12px 32px;
            border-radius: 20px;
            width: auto;
            height: auto;
        }

        .navbar.collapsed {
            padding: 0;
            border-radius: 50%;
            width: 60px;
            height: 60px;
        }

        .nav-links {
            display: flex;
            align-items: center;
            gap: 16px;
            opacity: 1;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .navbar.collapsed .nav-links {
            opacity: 0;
            pointer-events: none;
        }

        .nav-link {
            color: #374151;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            padding: 8px 12px;
            border-radius: 8px;
        }

        .nav-link:hover {
            color: #111827;
            background: rgba(0, 0, 0, 0.05);
            transform: translateY(-1px);
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-trigger {
            cursor: pointer;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            min-width: 180px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
            margin-top: 8px;
        }

        .nav-dropdown:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-link {
            display: block;
            color: #374151;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            padding: 12px 16px;
            transition: all 0.2s ease;
            border-radius: 8px;
            margin: 4px;
        }

        .dropdown-link:hover {
            color: #111827;
            background: rgba(0, 0, 0, 0.05);
            transform: translateX(4px);
        }

        .circle-button {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            pointer-events: none;
            transition: all 0.3s ease;
            cursor: pointer;
            background: none;
            border: none;
            outline: none;
            z-index: 10;
        }

        .navbar.collapsed .circle-button {
            opacity: 1;
            pointer-events: auto;
        }

        .circle-dot {
            width: 20px;
            height: 20px;
            background: #000000;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
        }

        .circle-button:hover .circle-dot {
            transform: scale(1.1);
        }

        .circle-dot::before {
            content: '';
            width: 6px;
            height: 6px;
            background: white;
            border-radius: 50%;
            position: absolute;
        }

        .dropdown {
            position: absolute;
            top: 70px;
            left: 50%;
            transform: translateX(-50%) translateY(-10px) scale(0.95);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1001;
        }

        .dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(0) scale(1);
        }

        .dropdown-item {
            display: block;
            color: #374151;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            padding: 12px 16px;
            transition: all 0.2s ease;
            border-radius: 8px;
            margin: 4px;
        }

        .dropdown-item:hover {
            color: #111827;
            background: rgba(0, 0, 0, 0.05);
            transform: translateX(4px);
        }

        .dropdown-section {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            margin-bottom: 8px;
            padding-bottom: 8px;
        }

        .dropdown-section:last-of-type {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .main-item {
            font-weight: 600;
            color: #111827;
        }

        .sub-item {
            font-size: 13px;
            padding-left: 24px;
            color: #6b7280;
        }

        .sub-item:hover {
            color: #374151;
        }

        .content {
            padding-top: 120px;
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
            padding-left: 24px;
            padding-right: 24px;
        }

        .title {
            font-size: 48px;
            font-weight: 700;
            color: #111827;
            margin-bottom: 16px;
        }

        .subtitle {
            font-size: 18px;
            color: #6b7280;
            margin-bottom: 80px;
        }

        .page-content {
            margin-top: 80px;
            padding: 48px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 16px;
            min-height: 150vh;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #111827;
            margin-bottom: 16px;
        }

        .page-text {
            font-size: 16px;
            color: #374151;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <nav class="navbar expanded" id="navbar">
        <div class="nav-links" id="navLinks">
            <a href="/" class="nav-link">Home</a>
            <a href="/about" class="nav-link">About Us</a>
            <div class="nav-dropdown">
                <a href="/services" class="nav-link dropdown-trigger">Services ▼</a>
                <div class="dropdown-menu">
                    <a href="/services/buy" class="dropdown-link">Buy Property</a>
                    <a href="/services/lease" class="dropdown-link">Lease Property</a>
                    <a href="/services/pre-lease" class="dropdown-link">Pre-Lease</a>
                    <a href="/services/sell" class="dropdown-link">Sell Property</a>
                    <a href="/services/investment" class="dropdown-link">Investment</a>
                </div>
            </div>
            <div class="nav-dropdown">
                <a href="/projects" class="nav-link dropdown-trigger">Projects ▼</a>
                <div class="dropdown-menu">
                    <a href="/projects/new" class="dropdown-link">New</a>
                    <a href="/projects/resale" class="dropdown-link">Resale</a>
                </div>
            </div>
            <a href="/blog" class="nav-link">Blog</a>
            <a href="/calculator" class="nav-link">Calculator</a>
            <a href="/career" class="nav-link">Career</a>
            <a href="/contact" class="nav-link">Contact Us</a>
        </div>
        
        <button class="circle-button" id="circleButton">
            <div class="circle-dot"></div>
        </button>
        
        <div class="dropdown" id="dropdown">
            <a href="/" class="dropdown-item">Home</a>
            <a href="/about" class="dropdown-item">About Us</a>
            <div class="dropdown-section">
                <a href="/services" class="dropdown-item main-item">Services</a>
                <a href="/services/buy" class="dropdown-item sub-item">• Buy Property</a>
                <a href="/services/lease" class="dropdown-item sub-item">• Lease Property</a>
                <a href="/services/pre-lease" class="dropdown-item sub-item">• Pre-Lease</a>
                <a href="/services/sell" class="dropdown-item sub-item">• Sell Property</a>
                <a href="/services/investment" class="dropdown-item sub-item">• Investment</a>
            </div>
            <div class="dropdown-section">
                <a href="/projects" class="dropdown-item main-item">Projects</a>
                <a href="/projects/new" class="dropdown-item sub-item">• New</a>
                <a href="/projects/resale" class="dropdown-item sub-item">• Resale</a>
            </div>
            <a href="/blog" class="dropdown-item">Blog</a>
            <a href="/calculator" class="dropdown-item">Calculator</a>
            <a href="/career" class="dropdown-item">Career</a>
            <a href="/contact" class="dropdown-item">Contact Us</a>
        </div>
    </nav>

    <div class="content">
        <h1 class="title">Dwelling Desire</h1>
        <p class="subtitle">Your Premium Real Estate Partner. Scroll down to see the navigation magic.</p>
        
        <div class="page-content">
            <h2 class="page-title">Welcome to Dwelling Desire</h2>
            <p class="page-text">
                We provide comprehensive real estate services including buying, selling, leasing, 
                and investment opportunities. Our advanced navigation system adapts to your browsing 
                experience with smooth, professional animations.
            </p>
        </div>
    </div>

    <script>
        const navbar = document.getElementById('navbar');
        const circleButton = document.getElementById('circleButton');
        const dropdown = document.getElementById('dropdown');
        let isDropdownOpen = false;

        // Scroll detection
        window.addEventListener('scroll', () => {
            const scrollY = window.scrollY;
            
            if (scrollY > 50) {
                navbar.classList.remove('expanded');
                navbar.classList.add('collapsed');
            } else {
                navbar.classList.remove('collapsed');
                navbar.classList.add('expanded');
                // Close dropdown when returning to expanded state
                dropdown.classList.remove('show');
                isDropdownOpen = false;
            }
        });

        // Circle button click
        circleButton.addEventListener('click', (e) => {
            e.stopPropagation();
            isDropdownOpen = !isDropdownOpen;
            console.log('Circle clicked! Dropdown open:', isDropdownOpen);
            
            if (isDropdownOpen) {
                dropdown.classList.add('show');
                console.log('Dropdown opened');
            } else {
                dropdown.classList.remove('show');
                console.log('Dropdown closed');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', () => {
            dropdown.classList.remove('show');
            isDropdownOpen = false;
        });

        // Prevent dropdown close when clicking inside it
        dropdown.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Close dropdown when clicking on dropdown items
        const dropdownItems = document.querySelectorAll('.dropdown-item');
        dropdownItems.forEach(item => {
            item.addEventListener('click', () => {
                dropdown.classList.remove('show');
                isDropdownOpen = false;
            });
        });
    </script>
</body>
</html> 