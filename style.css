@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}
a{
    text-decoration: none;
    color: black;
}
.container{
    width: 100%;
    /* height: 100vh;    */
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
}
nav{
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 50px;
    flex-direction: row;
}
.nav-links{
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
    list-style: none;
    gap: 2rem;
}
.nav-links ul li a{
    text-decoration: none;
}
.logo{
    width: 100px;
    height: 100px;
}
.logo img{
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Dropdown Styles */
.dropdown {
    position: relative;
}

.dropdown-toggle {
    cursor: pointer;
    transition: color 0.3s ease;
}

.dropdown-toggle:hover {
    color: #007bff;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    min-width: 200px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    padding: 10px 0;
    list-style: none;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    padding: 0;
}

.dropdown-menu li a {
    display: block;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 4px;
    margin: 0 8px;
}

.dropdown-menu li a:hover {
    background-color: #f8f9fa;
    color: #007bff;
    transform: translateX(5px);
}